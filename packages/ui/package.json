{"name": "@next-starter/ui", "version": "0.0.0", "private": true, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./hooks": "./src/hooks/index.ts"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo node_modules"}, "devDependencies": {"@next-starter/eslint-config": "workspace:*", "@next-starter/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.27.0", "typescript": "5.8.2"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@tailwindcss/postcss": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "zod": "^3.25.28"}}