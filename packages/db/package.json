{"name": "@next-starter/db", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "private": true, "description": "Database", "scripts": {"db:generate": "prisma generate", "db:migrate": "prisma migrate dev --skip-generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx db/seed.ts", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo node_modules"}, "license": "ISC", "devDependencies": {"@next-starter/eslint-config": "workspace:*", "@next-starter/typescript-config": "workspace:*", "@types/node": "^22.15.3", "prisma": "^6.8.2"}, "dependencies": {"@prisma/client": "^6.8.2"}, "exports": {".": "./src/index.ts", "./client": "./src/client.ts"}}