{"name": "next-app", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "dev:admin": "turbo run dev --filter=admin", "dev:client": "turbo run dev --filter=client", "db:push": "turbo run db:push", "db:generate": "turbo run db:generate", "db:studio": "turbo run db:studio", "check-types": "turbo run check-types", "clean": "turbo run clean && rm -rf node_modules"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}