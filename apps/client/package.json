{"name": "client", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3001 --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "clean": "rm -rf .next .turbo node_modules"}, "dependencies": {"@next-starter/ui": "workspace:*", "lucide-react": "^0.511.0", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@next-starter/eslint-config": "workspace:*", "@next-starter/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.27.0", "typescript": "5.8.2"}}