{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "dev:client": {"cache": false, "persistent": true}, "clean": {"cache": false}, "db:push": {"cache": false}, "db:generate": {"cache": false}, "db:studio": {"cache": false}}}